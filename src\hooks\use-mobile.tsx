import * as React from "react"

const M<PERSON><PERSON>LE_BREAKPOINT = 768
const TABLET_BREAKPOINT = 1024

export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    
    checkMobile() // Initial check
    
    mql.addEventListener("change", checkMobile)
    window.addEventListener('resize', checkMobile)
    
    return () => {
      mql.removeEventListener("change", checkMobile)
      window.removeEventListener('resize', checkMobile)
    }
  }, [])

  return !!isMobile
}
