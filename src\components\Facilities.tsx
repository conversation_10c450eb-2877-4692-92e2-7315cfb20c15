import { Card } from "@/components/ui/card";
import { Waves, Utensils, Dumbbell, Sparkles, Trees, GamepadIcon } from "lucide-react";
import { useImageSlideEffect } from "@/hooks/useImageSlideEffect";
import ImageSlider from "@/components/ImageSlider";

const Facilities = () => {
  const { visibleImages, imageRefs } = useImageSlideEffect();
  const facilities = [
    {
      icon: Waves,
      title: "Double & Plus Villalar",
      description: "Ultra lüks ve mahremiyet odaklı: Sadece size özel açık havuz keyfi. Her anı size özel, her detayı ultra lüks termal tatil.",
      images: ["/images/gallery/13-min-2.jpg", "/images/gallery/13-min-4.jpg", "/images/atv.jpg"]
    },
    {
      icon: Sparkles,
      title: "VK City Cafe & Bistro",
      description: "Tatilinize lezzet katan ayrıcalıklı bir deneyim: <PERSON>gin serp<PERSON> ka<PERSON>, pizza<PERSON>, hamburgerler, ve özel kahve se<PERSON>yle her damak zevkine hitap ediyor.",
      images: ["/images/gallery/13-min-4.jpg", "/images/gallery/13-min-2.jpg", "/images/atv.jpg"]
    },
    {
      icon: Utensils,
      title: "ATV, Fayton & Balon Turu",
      description: "Frig Vadisi’nde ATV, fayton ve balon turlarıyla doğanın keyfini çıkarın. Eşsiz manzaralar sizi bekliyor. Rezervasyon ve detaylı bilgi için lütfen bizimle iletişime geçin.",
      images: ["/images/atv.jpg", "/images/gallery/13-min-2.jpg", "/images/gallery/13-min-4.jpg"]
    },

  ];

  return (
    <section id="facilities" className="py-20 bg-thermal-light/20">
      <div className="container mx-auto px-4">
        <div className="text-center space-y-4 mb-16 animate-fade-in-up">
          <h2 className="text-4xl md:text-6xl  font-script rotate-[-3deg] text-thermal-dark">
            Aktivitelerimiz
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Lüks villa konaklamalarınızın yanı sıra unutulmaz deneyimler sunan çeşitli villa olanaklarımız.
          </p>
        </div>

                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 animate-fade-in-up">
          {facilities.map((facility, index) => (
            <Card key={index} className="group overflow-hidden shadow-card-thermal hover:shadow-thermal transition-all duration-300">
              <div
                ref={(el) => (imageRefs.current[index] = el)}
                data-index={index}
                className="relative overflow-hidden"
              >
                <ImageSlider
                  images={facility.images}
                  alt={facility.title}
                  className="w-full h-48"
                  autoPlay={true}
                  autoPlayInterval={6000}
                />
                <div className={`absolute inset-0 bg-gradient-to-${index % 4 === 0 ? 'r' : index % 4 === 1 ? 'b' : index % 4 === 2 ? 'l' : 't'} from-thermal-bronze/70 via-thermal-bronze/50 to-thermal-bronze/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(index) ?
                  (index % 4 === 0 ? 'translate-x-full' :
                   index % 4 === 1 ? 'translate-y-full' :
                   index % 4 === 2 ? '-translate-x-full' : '-translate-y-full') :
                  (index % 4 === 1 || index % 4 === 3 ? 'translate-y-0' : 'translate-x-0')
                }`}></div>
                <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm p-3 rounded-full">
                  <facility.icon className="w-6 h-6 text-thermal-bronze" />
                </div>
              </div>

              <div className="p-6 space-y-4">
                <h3 className="text-xl font-bold text-thermal-dark group-hover:text-thermal-bronze transition-colors">
                  {facility.title}
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {facility.description}
                </p>
              </div>
            </Card>
          ))}
        </div>

      </div>
    </section>
  );
};

export default Facilities;