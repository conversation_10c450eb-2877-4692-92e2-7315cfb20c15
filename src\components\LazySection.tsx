import { ReactNode } from 'react';
import { useLazyLoad } from '@/hooks/useLazyLoad';

interface LazySectionProps {
  children: ReactNode;
  fallback?: ReactNode;
  className?: string;
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
}

const LazySection = ({ 
  children, 
  fallback,
  className = '',
  threshold = 0.1,
  rootMargin = '100px',
  triggerOnce = true
}: LazySectionProps) => {
  const { elementRef, isVisible } = useLazyLoad({
    threshold,
    rootMargin,
    triggerOnce
  });

  const defaultFallback = (
    <div className={`min-h-[200px] flex items-center justify-center bg-thermal-light/30 ${className}`}>
      <div className="text-center space-y-4">
        <div className="w-12 h-12 border-4 border-thermal-blue border-t-transparent rounded-full animate-spin mx-auto"></div>
        <p className="text-thermal-blue font-medium">Yükleniyor...</p>
      </div>
    </div>
  );

  return (
    <div ref={elementRef} className={className}>
      {isVisible ? children : (fallback || defaultFallback)}
    </div>
  );
};

export default LazySection;
