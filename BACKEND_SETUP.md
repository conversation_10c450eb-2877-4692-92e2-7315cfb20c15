# Thermal Resort Backend Kurulum Rehberi

## 🚀 Hızlı Kurulum

### 1. <PERSON><PERSON>
```bash
mkdir thermal-resort-backend
cd thermal-resort-backend
npm init -y
```

### 2. <PERSON><PERSON><PERSON><PERSON>
```bash
# Ana bağımlılıklar
npm install express mysql2 cors helmet bcryptjs jsonwebtoken express-validator express-rate-limit multer sharp compression morgan dotenv nodemailer moment joi express-slow-down cookie-parser

# Development bağımlılıkları
npm install -D nodemon jest supertest
```

### 3. Klasör Yapısı Oluştur
```
thermal-resort-backend/
├── server.js
├── .env
├── package.json
├── routes/
│   ├── auth.js
│   ├── roomTypes.js
│   ├── reservations.js
│   ├── facilities.js
│   ├── gallery.js
│   ├── contact.js
│   ├── reviews.js
│   ├── seo.js
│   ├── settings.js
│   └── admin.js
├── middleware/
│   ├── auth.js
│   ├── validation.js
│   └── upload.js
├── utils/
│   ├── database.js
│   ├── email.js
│   └── helpers.js
├── migrations/
│   └── migrate.js
├── seeds/
│   └── seed.js
└── uploads/
    └── images/
```

### 4. MySQL Veritabanı Oluştur
```sql
-- MySQL'e bağlan ve veritabanı oluştur
CREATE DATABASE thermal_resort CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE thermal_resort;

-- database-schema.sql dosyasını çalıştır
SOURCE /path/to/database-schema.sql;
```

### 5. Environment Variables
```bash
# .env dosyasını oluştur ve env-example.txt'den kopyala
cp env-example.txt .env
# Kendi değerlerini gir
```

### 6. Sunucuyu Başlat
```bash
# Development mode
npm run dev

# Production mode
npm start
```

## 📋 Önemli SEO Optimizasyonları

### 1. Sitemap Otomatik Üretimi
- `/sitemap.xml` endpoint'i otomatik sitemap üretir
- Oda tipleri, SEO sayfaları ve statik sayfalar dahil
- Google Search Console'a ekle

### 2. Meta Tags API
- `/meta/:slug` endpoint'i her sayfa için meta bilgileri döner
- Frontend'de dinamik meta tag güncellemesi için kullan

### 3. Schema.org JSON-LD
```javascript
// Otomatik hotel ve review schema markup'ları
{
  "@context": "https://schema.org",
  "@type": "Hotel",
  "name": "Thermal Resort",
  "description": "Lüks termal tatil deneyimi"
}
```

### 4. Robots.txt
- `/robots.txt` endpoint'i SEO dostu robots.txt üretir
- Admin paneli ve API'leri crawling'den koruyor

## 🔒 Güvenlik Özellikleri

### 1. Rate Limiting
- Global: 100 request/15dk
- Contact form: 5 mesaj/15dk
- Login: 5 deneme/15dk

### 2. Authentication
- JWT token tabanlı
- bcryptjs ile şifre hash'leme
- Role-based access control

### 3. Input Validation
- Joi/express-validator ile tüm inputlar kontrol ediliyor
- SQL injection koruması
- XSS koruması

## 📊 Admin Panel Özellikleri

### Dashboard
- Günlük/aylık rezervasyon istatistikleri
- Gelir analizi
- En popüler oda tipleri
- Son yorumlar

### Rezervasyon Yönetimi
- Rezervasyon takvimi
- Check-in/check-out işlemleri
- Fiyat güncellemeleri
- Sezonluk fiyatlandırma

### İçerik Yönetimi
- Oda tipi düzenleme
- Galeri yönetimi
- SEO sayfa editörü
- İletişim mesajları

## 🛠️ Geliştirme İpuçları

### 1. Database Migration
```javascript
// migrations/migrate.js
const fs = require('fs');
const mysql = require('mysql2/promise');

const runMigration = async () => {
  const connection = await mysql.createConnection(dbConfig);
  const sql = fs.readFileSync('./database-schema.sql', 'utf8');
  await connection.execute(sql);
  console.log('Migration completed!');
};
```

### 2. Seed Data
```javascript
// seeds/seed.js - Test verileri eklemek için
const seedRoomTypes = async () => {
  const roomTypes = [
    {
      name: 'Standard Oda',
      slug: 'standard-oda',
      capacity: 2,
      base_price: 800
    }
    // ... diğer oda tipleri
  ];
  
  // Insert into database
};
```

### 3. Email Templates
```javascript
// utils/email.js
const sendReservationConfirmation = async (reservation) => {
  const emailTemplate = `
    <h1>Rezervasyon Onayı</h1>
    <p>Sayın ${reservation.guest_name},</p>
    <p>Rezervasyonunuz onaylanmıştır.</p>
    <p>Rezervasyon No: ${reservation.id}</p>
  `;
  
  // Send email with nodemailer
};
```

## 🔄 Frontend Entegrasyonu

### API Base URL
```javascript
// Frontend'de (React)
const API_BASE = 'http://localhost:3000/api';

// Rezervasyon oluşturma
const createReservation = async (data) => {
  const response = await fetch(`${API_BASE}/reservations`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  });
  return response.json();
};
```

### SEO Entegrasyonu
```javascript
// Meta tags güncellemesi
const updateMetaTags = async (slug) => {
  const response = await fetch(`${API_BASE}/meta/${slug}`);
  const meta = await response.json();
  
  document.title = meta.title;
  document.querySelector('meta[name="description"]').content = meta.description;
};
```

## 📈 Production Deployment

### 1. Environment
```bash
NODE_ENV=production
```

### 2. Process Manager (PM2)
```bash
npm install -g pm2
pm2 start server.js --name "thermal-resort-api"
```

### 3. Nginx Reverse Proxy
```nginx
server {
    listen 80;
    server_name api.thermalresort.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 4. SSL Certificate
```bash
sudo certbot --nginx -d api.thermalresort.com
```

Bu yapı ile SEO optimized, güvenli ve ölçeklenebilir bir termal tesis rezervasyon sistemi backend'i elde edersin. Frontend'deki Lovable projen ile bu API'yi entegre etmek için sadece fetch çağrıları yapman yeterli!