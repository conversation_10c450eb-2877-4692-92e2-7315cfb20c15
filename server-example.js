const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const mysql = require('mysql2/promise');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Database connection
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'thermal_resort',
  charset: 'utf8mb4',
  timezone: '+03:00'
};

// Global rate limiting
const globalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});

// Middlewares
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:5173',
  credentials: true
}));
app.use(compression());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(globalLimiter);

// Database middleware
app.use(async (req, res, next) => {
  try {
    req.db = await mysql.createConnection(dbConfig);
    next();
  } catch (error) {
    console.error('Database connection error:', error);
    res.status(500).json({ error: 'Database connection failed' });
  }
});

// Routes
app.get('/', (req, res) => {
  res.json({
    message: 'Thermal Resort API',
    version: '1.0.0',
    status: 'active',
    endpoints: {
      auth: '/api/auth',
      rooms: '/api/room-types',
      reservations: '/api/reservations',
      facilities: '/api/facilities',
      gallery: '/api/gallery',
      contact: '/api/contact',
      reviews: '/api/reviews'
    }
  });
});

// SEO Routes
app.get('/sitemap.xml', async (req, res) => {
  try {
    const [roomTypes] = await req.db.execute('SELECT slug, updated_at FROM room_types WHERE is_active = 1');
    const [seoPages] = await req.db.execute('SELECT slug, updated_at FROM seo_pages WHERE is_active = 1');
    
    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${process.env.FRONTEND_URL}/</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>`;

    // Add room type pages
    roomTypes.forEach(room => {
      sitemap += `
  <url>
    <loc>${process.env.FRONTEND_URL}/rooms/${room.slug}</loc>
    <lastmod>${room.updated_at}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`;
    });

    // Add SEO pages
    seoPages.forEach(page => {
      sitemap += `
  <url>
    <loc>${process.env.FRONTEND_URL}/${page.slug}</loc>
    <lastmod>${page.updated_at}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`;
    });

    sitemap += '\n</urlset>';
    
    res.header('Content-Type', 'application/xml');
    res.send(sitemap);
  } catch (error) {
    console.error('Sitemap error:', error);
    res.status(500).json({ error: 'Sitemap generation failed' });
  }
});

app.get('/robots.txt', (req, res) => {
  const robots = `User-agent: *
Disallow: /admin/
Disallow: /api/
Allow: /

Sitemap: ${process.env.FRONTEND_URL}/sitemap.xml`;
  
  res.header('Content-Type', 'text/plain');
  res.send(robots);
});

// API Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/room-types', require('./routes/roomTypes'));
app.use('/api/reservations', require('./routes/reservations'));
app.use('/api/facilities', require('./routes/facilities'));
app.use('/api/gallery', require('./routes/gallery'));
app.use('/api/contact', require('./routes/contact'));
app.use('/api/reviews', require('./routes/reviews'));
app.use('/api/seo', require('./routes/seo'));
app.use('/api/settings', require('./routes/settings'));
app.use('/api/admin', require('./routes/admin'));

// Meta tags API for SEO
app.get('/meta/:slug', async (req, res) => {
  try {
    const { slug } = req.params;
    
    // Check if it's a room type
    const [roomType] = await req.db.execute(
      'SELECT name, description, image_url FROM room_types WHERE slug = ? AND is_active = 1',
      [slug]
    );
    
    if (roomType.length > 0) {
      const room = roomType[0];
      return res.json({
        title: `${room.name} - Thermal Resort`,
        description: room.description || `${room.name} ile termal tatil keyfi yaşayın`,
        canonical: `${process.env.FRONTEND_URL}/rooms/${slug}`,
        og_image: room.image_url || `${process.env.FRONTEND_URL}/og-default.jpg`,
        schema: {
          "@context": "https://schema.org",
          "@type": "Hotel",
          "name": room.name,
          "description": room.description
        }
      });
    }
    
    // Check SEO pages
    const [seoPage] = await req.db.execute(
      'SELECT * FROM seo_pages WHERE slug = ? AND is_active = 1',
      [slug]
    );
    
    if (seoPage.length > 0) {
      const page = seoPage[0];
      return res.json({
        title: page.title,
        description: page.meta_description,
        canonical: page.canonical_url || `${process.env.FRONTEND_URL}/${slug}`,
        og_image: page.og_image || `${process.env.FRONTEND_URL}/og-default.jpg`
      });
    }
    
    res.status(404).json({ error: 'Page not found' });
  } catch (error) {
    console.error('Meta tags error:', error);
    res.status(500).json({ error: 'Meta tags generation failed' });
  }
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Error:', error);
  
  if (req.db) {
    req.db.end();
  }
  
  res.status(error.status || 500).json({
    error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
  });
});

// Close database connections
process.on('SIGINT', async () => {
  console.log('Shutting down server...');
  process.exit(0);
});

app.listen(PORT, () => {
  console.log(`🚀 Thermal Resort API running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🌐 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
});