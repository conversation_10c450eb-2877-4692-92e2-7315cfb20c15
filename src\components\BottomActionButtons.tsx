import { MessageCircle, Phone, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";

const BottomActionButtons = () => {
  const whatsappNumber = "+902724124860"; // Gerçek WhatsApp numarası buraya eklenecek
  const message = "Merhaba! Villa Kent Termal Otel rezervasyonu hakkında bilgi almak istiyorum.";

  const handleWhatsApp = () => {
    const url = `https://wa.me/${whatsappNumber.replace(/[^\d]/g, '')}?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
  };

  const handleCall = () => {
    window.open(`tel:${whatsappNumber}`, '_self');
  };

  const handleReservation = () => {
    // Rezervasyon bölümüne scroll yap
    const reservationSection = document.getElementById('reservation');
    if (reservationSection) {
      reservationSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className=" lg:hidden fixed bottom-0 left-0 right-0 z-50 bg-gradient-to-br from-blue-50/70 via-green-50/50 to-teal-50/60 border-t border-blue-200/50 shadow-lg">
      <div className="flex">
        {/* WhatsApp Butonu */}
        <Button
          onClick={handleWhatsApp}
          className="flex-1 h-12  bg-green-500 hover:bg-green-600 text-white text-xs transition-colors duration-300 rounded-none border-r border-green-400"
        >
          <span className="flex-1 text-left">WhatsApp</span>
          <MessageCircle className="w-5 h-5 ml-1" />
        </Button>

        {/* Hemen Ara Butonu */}
        <Button
          onClick={handleCall}
          className="flex-1 h-12 bg-blue-500 hover:bg-blue-600 text-white text-xs transition-colors duration-300 rounded-none border-r border-blue-400"
        >
          <span className="flex-1 text-left">Hemen Ara</span>
          <Phone className="w-5 h-5 ml-1" />
        </Button>

        {/* Rezervasyon Butonu */}
        <Button
          onClick={handleReservation}
          className="flex-1 h-12 bg-thermal-bronze hover:bg-thermal-bronze-light text-white text-xs transition-colors duration-300 rounded-none"
        >
          <span className="flex-1 text-left">Rezervasyon</span>
          <Calendar className="w-5 h-5 ml-1" />
        </Button>
      </div>
    </div>
  );
};

export default BottomActionButtons;