import { Phone, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";

const BottomActionButtons = () => {
  const whatsappNumber = "+902724124860"; // Gerçek WhatsApp numarası buraya eklenecek
  const message = "Merhaba! Villa Kent Termal Otel rezervasyonu hakkında bilgi almak istiyorum.";

  const handleWhatsAppCall = () => {
    const url = `https://wa.me/${whatsappNumber.replace(/[^\d]/g, '')}?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
  };

  const handleReservation = () => {
    // Rezervasyon bölümüne scroll yap
    const reservationSection = document.getElementById('reservation');
    if (reservationSection) {
      reservationSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 shadow-lg">
      <div className="flex">
        {/* WhatsApp Hemen Ara Butonu */}
        <Button
          onClick={handleWhatsAppCall}
          className="flex-1 h-16 bg-green-500 hover:bg-green-600 text-white font-semibold text-base transition-colors duration-300 rounded-none border-r border-green-400"
        >
          <span className="flex-1 text-left">Hemen Ara</span>
          <Phone className="w-6 h-6 ml-2" />
        </Button>

        {/* Rezervasyon Butonu */}
        <Button
          onClick={handleReservation}
          className="flex-1 h-16 bg-thermal-bronze hover:bg-thermal-bronze-light text-white font-semibold text-base transition-colors duration-300 rounded-none"
        >
          <span className="flex-1 text-left">Rezervasyon</span>
          <Calendar className="w-6 h-6 ml-2" />
        </Button>
      </div>
    </div>
  );
};

export default BottomActionButtons;