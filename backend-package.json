{"name": "thermal-resort-backend", "version": "1.0.0", "description": "Thermal Resort Backend API with Express.js and MySQL", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "db:migrate": "node migrations/migrate.js", "db:seed": "node seeds/seed.js"}, "keywords": ["thermal", "resort", "booking", "api", "express", "mysql"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "nodemailer": "^6.9.8", "moment": "^2.29.4", "joi": "^17.11.0", "express-slow-down": "^2.0.1", "cookie-parser": "^1.4.6"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}