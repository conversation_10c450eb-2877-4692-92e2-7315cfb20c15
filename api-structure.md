# Thermal Resort API Structure
Express.js + MySQL Backend API Endpoints

## Base URL
```
http://localhost:3000/api
```

## Authentication Endpoints

### POST /auth/register
**<PERSON>llanıcı kayıt**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "<PERSON><PERSON>",
  "last_name": "<PERSON><PERSON><PERSON><PERSON>",
  "phone": "+90 555 123 45 67"
}
```

### POST /auth/login
**<PERSON>llanıcı giriş**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### POST /auth/logout
**Çıkış yap**

### GET /auth/me
**Kullanıcı bilgilerini getir**

---

## Room Types (Oda Tipleri)

### GET /room-types
**Tüm oda tiplerini listele**
- Query params: `?active=true&limit=10&offset=0`

### GET /room-types/:slug
**Oda tipi detayı**

### POST /room-types
**<PERSON><PERSON> oda tipi ekle (Admin)**
```json
{
  "name": "Deluxe Suite",
  "slug": "deluxe-suite",
  "description": "Lüks süit oda",
  "capacity": 4,
  "base_price": 1500.00,
  "features": ["WiFi", "Klima", "Jacuzzi"],
  "image_url": "https://example.com/image.jpg"
}
```

### PUT /room-types/:id
**Oda tipi güncelle (Admin)**

### DELETE /room-types/:id
**Oda tipi sil (Admin)**

---

## Availability & Pricing (Müsaitlik ve Fiyat)

### GET /availability
**Müsait odaları kontrol et**
- Query params: `check_in=2024-03-15&check_out=2024-03-18&adults=2&children=1`

### GET /pricing
**Fiyat hesapla**
- Query params: `room_type_id=1&check_in=2024-03-15&check_out=2024-03-18&adults=2&children=1`

---

## Reservations (Rezervasyonlar)

### GET /reservations
**Rezervasyonları listele**
- Query params: `?user_id=1&status=confirmed&page=1&limit=10`

### GET /reservations/:id
**Rezervasyon detayı**

### POST /reservations
**Yeni rezervasyon oluştur**
```json
{
  "room_type_id": 1,
  "check_in_date": "2024-03-15",
  "check_out_date": "2024-03-18",
  "adults": 2,
  "children": 1,
  "guest_name": "Ahmet Yılmaz",
  "guest_email": "<EMAIL>",
  "guest_phone": "+90 555 123 45 67",
  "special_requests": "Yüksek kat tercihi"
}
```

### PUT /reservations/:id
**Rezervasyon güncelle**

### PUT /reservations/:id/status
**Rezervasyon durumu güncelle (Admin)**
```json
{
  "status": "confirmed"
}
```

### DELETE /reservations/:id
**Rezervasyon iptal et**

---

## Facilities (Tesisler)

### GET /facilities
**Tesis özelliklerini listele**

### POST /facilities
**Yeni tesis özelliği ekle (Admin)**
```json
{
  "name": "Termal Havuz",
  "slug": "termal-havuz",
  "description": "Doğal termal kaynaklı havuz",
  "image_url": "https://example.com/pool.jpg",
  "icon": "Waves"
}
```

### PUT /facilities/:id
**Tesis özelliği güncelle (Admin)**

### DELETE /facilities/:id
**Tesis özelliği sil (Admin)**

---

## Gallery (Galeri)

### GET /gallery
**Galeri resimlerini listele**
- Query params: `?category=rooms&featured=true&limit=20`

### POST /gallery
**Yeni resim ekle (Admin)**
```json
{
  "title": "Deluxe Suite",
  "image_url": "https://example.com/image.jpg",
  "alt_text": "Lüks süit oda görünümü",
  "category": "rooms",
  "is_featured": true
}
```

### PUT /gallery/:id
**Resim güncelle (Admin)**

### DELETE /gallery/:id
**Resim sil (Admin)**

---

## Contact (İletişim)

### POST /contact
**İletişim mesajı gönder**
```json
{
  "name": "Ahmet Yılmaz",
  "email": "<EMAIL>",
  "phone": "+90 555 123 45 67",
  "preferred_contact_time": "afternoon",
  "message": "Rezervasyon hakkında bilgi almak istiyorum"
}
```

### GET /contact (Admin)
**İletişim mesajlarını listele**
- Query params: `?status=new&page=1&limit=10`

### PUT /contact/:id/status (Admin)
**Mesaj durumu güncelle**
```json
{
  "status": "replied"
}
```

---

## Reviews (Yorumlar)

### GET /reviews
**Onaylı yorumları listele**
- Query params: `?rating=5&limit=10`

### POST /reviews
**Yorum ekle**
```json
{
  "reservation_id": 123,
  "guest_name": "Ahmet Yılmaz",
  "rating": 5,
  "title": "Harika deneyim",
  "comment": "Çok memnun kaldık, tekrar geleceğiz"
}
```

### PUT /reviews/:id/approve (Admin)
**Yorum onayla**

---

## SEO & Content (SEO ve İçerik)

### GET /seo/:slug
**SEO sayfası getir**

### POST /seo (Admin)
**Yeni SEO sayfası oluştur**
```json
{
  "slug": "termal-tatil-rehberi",
  "title": "Termal Tatil Rehberi - En İyi İpuçları",
  "meta_description": "Termal tatil için en kapsamlı rehber. Sağlık, rahatlama ve keyif bir arada.",
  "h1_title": "Termal Tatil Rehberi",
  "content": "<h2>Termal Sular ve Faydaları</h2>...",
  "canonical_url": "https://thermalresort.com/termal-tatil-rehberi"
}
```

---

## Settings (Ayarlar)

### GET /settings
**Site ayarlarını getir**

### PUT /settings (Admin)
**Ayarları güncelle**
```json
{
  "contact_phone": "+90 555 999 88 77",
  "site_title": "Yeni Site Başlığı"
}
```

---

## Dashboard & Analytics (Admin)

### GET /admin/dashboard
**Admin dashboard verileri**

### GET /admin/analytics
**İstatistikler**
- Query params: `?start_date=2024-03-01&end_date=2024-03-31`

### GET /admin/reservations/calendar
**Rezervasyon takvimi**
- Query params: `?year=2024&month=3`

---

## File Upload

### POST /upload/image
**Resim yükle**
- Multipart form data
- Max size: 5MB
- Allowed: jpg, jpeg, png, webp

### DELETE /upload/:filename
**Dosya sil (Admin)**

---

## Middleware & Security

### Authentication
```javascript
// JWT token kontrolü
const authenticateToken = (req, res, next) => {
  // JWT verification logic
}

// Admin kontrolü  
const requireAdmin = (req, res, next) => {
  // Admin role check
}
```

### Rate Limiting
```javascript
// Rate limiting for API endpoints
const rateLimit = require('express-rate-limit');

const contactLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5 // 5 requests per 15 minutes
});
```

### Validation
```javascript
// Request validation with Joi or express-validator
const { body, validationResult } = require('express-validator');

const validateReservation = [
  body('guest_email').isEmail(),
  body('check_in_date').isDate(),
  body('adults').isInt({ min: 1, max: 10 })
];
```

---

## SEO Optimizations

### Sitemap Generation
```javascript
// GET /sitemap.xml
// Automatically generate sitemap with:
// - Room type pages
// - SEO content pages  
// - Static pages
```

### Robots.txt
```javascript
// GET /robots.txt
// Dynamic robots.txt generation
```

### Schema.org JSON-LD
```javascript
// Hotel schema markup for room types
// Review schema markup for testimonials
// LocalBusiness schema for contact info
```

### Meta Tags API
```javascript
// GET /meta/:slug
// Return meta tags for any page/slug
{
  "title": "Deluxe Suite - Thermal Resort",
  "description": "Lüks süit odalar ile termal tatil keyfi",
  "canonical": "https://thermalresort.com/rooms/deluxe-suite",
  "og_image": "https://thermalresort.com/images/deluxe-suite.jpg"
}
```