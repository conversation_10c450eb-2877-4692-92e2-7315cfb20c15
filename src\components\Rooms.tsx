import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Bed, Users, Wifi, Car, Coffee, Bath, Baby } from "lucide-react";
import { useImageSlideEffect } from "@/hooks/useImageSlideEffect";
const Rooms = () => {
  const { visibleImages, imageRefs } = useImageSlideEffect();
  const rooms = [
    {
      id: 1,
      name: "Standart Villa",
      price: "9.900",
      originalPrice: "9.200",
      image: "/images/rooms/20-min-3.jpg",
      capacity: "6 Yetişkin",
      childrenCapacity: "2 Çoçuk (6-12 Yaş)",
      features: ["Termal Havuz", "Jeotermal Isıtma", "Akıllı TV", "Tam Donanımlı Mutfak"],
      description: "Modern yaşamın tüm imkanlarıyla donatılmış, termal suların iyileştirici gücüyle birleşen villa."
    },
    {
      id: 2,
      name: "Luxury Villa",
      price: "8.900",
      originalPrice: "10.500",
      image: "/images/rooms/21-min-1.jpg",
      capacity: "6 Yetişkin",
      childrenCapacity: "2 <PERSON><PERSON><PERSON>uk (6-12 Yaş)",
      features: ["Yarı Açık Havuz", "Kış Bahçesi", "Yüksek Hız İnternet", "Netflix"],
      description: "Eşsiz bir tatil deneyimi sunan luxury villalarımızda konfor ve lüksün harmonisini yaşayın."
    },
    {
      id: 3,
      name: "Deluxe Villa",
      price: "11.900",
      originalPrice: "12.000",
      image: "/images/rooms/21-min-2.jpg",
      capacity: "6 Yetişkin",
      childrenCapacity: "2 Çoçuk (6-12 Yaş)",
      features: ["Özgünlük", "Şıklık", "Villa Havuzu", "Kış Bahçesi"],
      description: "Özgünlük ve şıklığın buluştuğu deluxe villalarımızda mükemmel konaklama."
    },
    {
      id: 4,
      name: "Exclusive Villa",
      price: "16.900",
      originalPrice: "13.500",
      image: "/images/rooms/25.jpg",
      capacity: "6 Yetişkin",
      childrenCapacity: "2 Çoçuk (6-12 Yaş)",
      features: ["Sadece Size Özel", "Bahçe Havuzu", "Yarı Açık Havuz", "Villa Havuzu"],
      description: "Sadece size özel exclusive villalarımızda özel bir tatil deneyimi yaşayın."
    },
    {
      id: 5,
      name: "Exclusive Plus",
      price: "19.900",
      originalPrice: "15.000",
      image: "/images/rooms/13-min-2.jpg",
      capacity: "6 Yetişkin",
      childrenCapacity: "2 Çoçuk (6-12 Yaş)",
      features: ["Mükemmellikte Konfor", "Bahçe Havuzu", "Villa Havuzu", "Sauna + Jakuzi"],
      description: "Konfor mükemmelliğin ötesinde olan exclusive plus villalarımızla unutulmaz anlar."
    }
  ];

  return (
    <section id="rooms" className="py-16 sm:py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="text-center space-y-4 mb-16 animate-fade-in-up">
          <div className="inline-block px-4 py-2 bg-thermal-bronze/10 text-thermal-bronze rounded-full font-semibold">
            Konaklama
          </div>
          <h2 className="text-4xl md:text-5xl italic font-bold text-thermal-dark">
           Kent Villalarımız
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            44 zarif villa ile benzersiz bir tatil deneyimi. Modern yaşamın tüm imkanlarıyla donatılmış özel termal havuzlu villalar.
          </p>
        </div>

                 <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 animate-fade-in-up">
            {rooms.map((room, index) => (
              <Card key={room.id} className="overflow-hidden shadow-card-thermal hover:shadow-thermal transition-all duration-300 group">
                <div 
                  ref={(el) => (imageRefs.current[index] = el)}
                  data-index={index}
                  className="relative overflow-hidden"
                >
                  <img
                    src={room.image}
                    alt={room.name}
                    className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className={`absolute inset-0 bg-gradient-to-${index === 0 ? 'r' : index === 1 ? 'b' : 'l'} from-thermal-bronze/70 via-thermal-bronze/50 to-thermal-bronze/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(index) ? (index === 0 ? 'translate-x-full' : index === 1 ? 'translate-y-full' : '-translate-x-full') : (index === 1 ? 'translate-y-0' : 'translate-x-0')}`}></div>
                  <div className="absolute top-4 right-4 bg-thermal-bronze-light text-white px-3 py-1 rounded-full font-bold text-sm">
                    %15 İndirim
                  </div>
                </div>

                <div className="p-6 space-y-4">
                  <div className="space-y-2">
                    <h3 className="text-xl font-bold text-thermal-dark">{room.name}</h3>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Users className="w-4 h-4" />
                      <span>{room.capacity}</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Baby className="w-4 h-4" />
                      <span>{room.childrenCapacity}</span>
                    </div>
                  </div>

                  <p className="text-muted-foreground text-sm">
                    {room.description}
                  </p>

                  <div className="grid grid-cols-2 gap-2">
                    {room.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <div className="w-2 h-2 bg-thermal-bronze rounded-full"></div>
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  <div className="border-t pt-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm text-muted-foreground line-through">
                          {room.originalPrice} TL
                        </div>
                        <div className="text-2xl font-bold text-thermal-bronze">
                          {room.price} TL
                        </div>
                        <div className="text-sm text-muted-foreground">
                          / gecelik
                        </div>
                      </div>
                    </div>

                    <Button variant="thermal" className="w-full">
                      Rezervasyon Yap
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
        </div>

                 <div className="text-center mt-12 animate-fade-in-up">
          <div className="inline-flex flex-col sm:flex-row items-center gap-4 bg-thermal-light/50 backdrop-blur-sm p-4 rounded-xl">
            <div className="flex items-center gap-2">
              <Wifi className="w-5 h-5 text-thermal-bronze" />
              <span className="font-medium">Ücretsiz WiFi</span>
            </div>
            <div className="flex items-center gap-2">
              <Car className="w-5 h-5 text-thermal-bronze" />
              <span className="font-medium">Ücretsiz Otopark</span>
            </div>
            <div className="flex items-center gap-2">
              <Coffee className="w-5 h-5 text-thermal-bronze" />
              <span className="font-medium">Ücretli VK City Cafe & Bistro</span>
            </div>
            <div className="flex items-center gap-2">
              <Bath className="w-5 h-5 text-thermal-bronze" />
              <span className="font-medium">Termal Havuz</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Rooms;