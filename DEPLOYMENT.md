# CapRover Deployment Guide

Bu proje CapRover ile deploy edilmek için hazırlanmıştır.

## Gerekli Dosyalar

Aşağıdaki dosyalar CapRover deployment için oluşturulmuştur:

- `Dockerfile` - Docker container tanımı
- `captain-definition` - CapRover deployment konfigürasyonu
- `.dockerignore` - Docker build'den hariç tutulacak dosyalar

## Deployment Adımları

### 1. CapRover Kurulumu
CapRover'ın sunucunuzda kurulu olduğundan emin olun.

### 2. Uygulama Oluşturma
CapRover dashboard'unda yeni bir uygulama oluşturun:
- App Name: `termal-hotel` (veya istediğiniz isim)
- Has Persistent Data: No

### 3. Deploy Etme

#### Yöntem 1: Tarball Upload
```bash
# Projeyi zip'leyin (node_modules hariç)
tar --exclude='node_modules' --exclude='.git' -czf termal-hotel.tar.gz .

# CapRover dashboard'unda "Deploy via Upload" seçeneğini kullanın
# termal-hotel.tar.gz dosyasını upload edin
```

#### Yöntem 2: Git Repository
```bash
# Git repository'nizi CapRover'a bağlayın
# CapRover dashboard'unda "Deploy via Git" seçeneğini kullanın
```

#### Yöntem 3: CLI ile Deploy
```bash
# CapRover CLI'yi kurun
npm install -g caprover

# Login olun
caprover login

# Deploy edin
caprover deploy
```

### 4. Domain Ayarları
- CapRover dashboard'unda uygulamanızın ayarlarına gidin
- "HTTP Settings" bölümünde domain'inizi ekleyin
- SSL sertifikası için "Enable HTTPS" seçeneğini aktifleştirin

### 5. Environment Variables (Opsiyonel)
Eğer environment variable'lar gerekiyorsa:
- CapRover dashboard'unda "App Configs" bölümüne gidin
- Environment Variables kısmına gerekli değişkenleri ekleyin

## Özellikler

- **Multi-stage Docker build**: Küçük production image
- **CapRover nginx**: CapRover'ın kendi nginx konfigürasyonu
- **Client-side routing**: React Router desteği
- **Optimized build**: Vite ile optimize edilmiş production build

## Troubleshooting

### Build Hatası
Eğer build sırasında hata alırsanız:
```bash
# Local'de test edin
npm run build
```

### Memory Hatası
Eğer build sırasında memory hatası alırsanız, CapRover'da app'in memory limitini artırın.

### Static Files Yüklenmiyor
Nginx konfigürasyonunu kontrol edin ve static file path'lerinin doğru olduğundan emin olun.

## Production Optimizasyonları

Proje aşağıdaki optimizasyonlarla hazırlanmıştır:
- Vite build optimizasyonu
- Tree shaking
- Code splitting
- Asset optimization
- Gzip compression
- Browser caching
