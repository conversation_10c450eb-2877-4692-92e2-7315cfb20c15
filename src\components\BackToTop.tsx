import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronUp } from "lucide-react";

const BackToTop = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);

    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <div
      className={`fixed bottom-36 right-4 z-40 transition-all duration-300 ${
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2 pointer-events-none"
      }`}
    >
      <Button
        onClick={scrollToTop}
        className="w-12 h-12 rounded-full bg-thermal-blue hover:bg-thermal-blue/90 text-white shadow-lg hover:shadow-xl transition-all duration-300 p-0"
      >
        <ChevronUp className="w-6 h-6" />
      </Button>
    </div>
  );
};

export default BackToTop;
