import { Card } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";
import { useImageSlideEffect } from "@/hooks/useImageSlideEffect";

const About = () => {
  const { visibleImages, imageRefs } = useImageSlideEffect();
  const features = [
    "44 zarif villa ile benzersiz deneyim",
    "Özel termal havuzlar ve geniş bahçeler", 
    "Jeotermal zemin ısıtma sistemi",
    "Yüksek hızda internet ve akıllı TV",
    "Tam donanımlı mutfak ve şık dekorasyon",
    "Frig Vadisi'nin eşsiz manzarası"
  ];

  return (
    <section id="about" className="py-20 bg-thermal-light/30">
      <div className="container mx-auto px-4">
                 <div className="grid lg:grid-cols-2 gap-12 items-center animate-fade-in-up">
          {/* Left Content */}
          <div className="space-y-8 animate-fade-in-left">
            <div className="space-y-4">
              <div className="inline-block px-4 py-2 bg-thermal-bronze/10 text-thermal-bronze rounded-full font-semibold">
                Hakkımızda
              </div>
              <h2 className="text-4xl italic md:text-5xl font-bold text-thermal-dark">
                Villa KENT
                <span className="block text-thermal-bronze">Termal Otel</span>
              </h2>
              <p className="text-lg text-muted-foreground leading-relaxed">
                Afyonkarahisar'ın büyüleyici doğası içinde yer alan Villa KENT, 44 zarif villa ile 
                benzersiz bir tatil deneyimi sunar. Modern yaşamın tüm imkanlarıyla donatılmış bu villalar, 
                termal suların iyileştirici gücüyle birleşerek, konfor ve lüksü bir arada yaşatır.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-thermal-bronze flex-shrink-0" />
                  <span className="text-foreground font-medium">{feature}</span>
                </div>
              ))}
            </div>

            <div className="bg-thermal-gradient p-6 rounded-xl text-white">
              <h3 className="text-xl font-bold mb-2">Lüks ve Konforun Harmonisi</h3>
              <p className="text-white/90">
                Her villa, şık dekorasyon, tam donanımlı mutfak ve özel termal havuzlarla zenginleştirilmiştir. 
                Mevsim ne olursa olsun ideal konaklama için jeotermal zemin ısıtma sistemi bulunur. 
                Doğayla iç içe huzurlu anlar sunan bu özel mekânda, unutulmaz tatil deneyimi sizleri bekliyor.
              </p>
            </div>
          </div>

          {/* Right Content - Images */}
          <div className="grid grid-cols-2 gap-4 animate-fade-in-right">
            <div className="space-y-4">
              <Card 
                ref={(el) => (imageRefs.current[0] = el)}
                data-index={0}
                className="relative overflow-hidden shadow-card-thermal"
              >
                <img
                  src="/images/gallery/1-min.jpg"
                  alt="Termal Otel Dış Görünüm"
                  className="w-full h-48 object-cover hover:scale-105 transition-transform duration-500"
                />
                <div className={`absolute inset-0 bg-gradient-to-r from-thermal-bronze/70 via-thermal-bronze/50 to-thermal-bronze/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(0) ? 'translate-x-full' : 'translate-x-0'}`}></div>
              </Card>
              <Card 
                ref={(el) => (imageRefs.current[1] = el)}
                data-index={1}
                className="relative overflow-hidden shadow-card-thermal"
              >
                <img
                  src="/images/gallery/4-min-1.jpg"
                  alt="Termal Havuz"
                  className="w-full h-32 object-cover hover:scale-105 transition-transform duration-500"
                />
                <div className={`absolute inset-0 bg-gradient-to-b from-thermal-bronze/70 via-thermal-bronze/50 to-thermal-bronze/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(1) ? 'translate-y-full' : 'translate-y-0'}`}></div>
              </Card>
            </div>
            <div className="space-y-4 pt-8">
              <Card 
                ref={(el) => (imageRefs.current[2] = el)}
                data-index={2}
                className="relative overflow-hidden shadow-card-thermal"
              >
                <img
                  src="/images/gallery/7-min-2.jpg"
                  alt="Spa & Wellness"
                  className="w-full h-32 object-cover hover:scale-105 transition-transform duration-500"
                />
                <div className={`absolute inset-0 bg-gradient-to-l from-thermal-bronze/70 via-thermal-bronze/50 to-thermal-bronze/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(2) ? '-translate-x-full' : 'translate-x-0'}`}></div>
              </Card>
              <Card 
                ref={(el) => (imageRefs.current[3] = el)}
                data-index={3}
                className="relative overflow-hidden shadow-card-thermal"
              >
                <img
                  src="/images/gallery/10-min-2.jpg"
                  alt="Otel Odası"
                  className="w-full h-48 object-cover hover:scale-105 transition-transform duration-500"
                />
                <div className={`absolute inset-0 bg-gradient-to-t from-thermal-bronze/70 via-thermal-bronze/50 to-thermal-bronze/30 transition-transform duration-1000 ease-in-out ${visibleImages.has(3) ? '-translate-y-full' : 'translate-y-0'}`}></div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;