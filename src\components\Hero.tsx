import { Button } from "@/components/ui/button";
import { Calendar, Phone } from "lucide-react";

const Hero = () => {
  return (
    <section id="hero" className="h-[530px] lg:h-[850px] relative overflow-hidden" role="banner" aria-label="Villa Kent Termal Otel Ana Sayfa">
      {/* Desktop Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat hidden lg:block "
        style={{
          backgroundImage: `url('/images/hero/0-min.jpg')`
        }}
        role="img"
        aria-label="Villa KENT Termal Otel Gazlıgöl Afyonkarahisar"
      />
      
      {/* Mobile Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat lg:hidden"
        style={{
          backgroundImage: `url('/images/hero/0-min.jpg')`
        }}
        role="img"
        aria-label="Villa KENT Termal Otel Mobil Görünüm"
      />

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-bl from-black/60 via-black/40 to-transparent"></div>

      {/* Content */}
      <div className="relative z-10 h-full flex items-center justify-center text-center">
        <div className="max-w-4xl px-4">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold italic text-white mb-4">
            Villa KENT Termal Otel
          </h1>
          <h2 className="text-3xl text-white sm:text-4xl lg:text-6xl text-thermal-gold font-semibold mb-6 font-script rotate-[-3deg]">
            Sıra Dışı Termal Tatil
          </h2>
          <p className="text-sm sm:text-xl text-white/90 max-w-2xl mx-auto bg-black/40 p-2 rounded-lg">
            Ultra lüks villalar, özel termal havuzlar ve huzurlu spa ile unutulmaz bir konaklama sizi bekliyor
          </p>
        </div>
      </div>
    </section>
  );
};

export default Hero;