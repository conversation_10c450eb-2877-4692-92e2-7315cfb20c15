# Cache Control Headers
/images/*
  Cache-Control: public, max-age=31536000, immutable

/assets/*
  Cache-Control: public, max-age=31536000, immutable

*.js
  Cache-Control: public, max-age=31536000, immutable

*.css
  Cache-Control: public, max-age=31536000, immutable

*.webp
  Cache-Control: public, max-age=31536000, immutable

*.png
  Cache-Control: public, max-age=31536000, immutable

*.jpg
  Cache-Control: public, max-age=31536000, immutable

*.ico
  Cache-Control: public, max-age=31536000, immutable

# HTML için daha kısa cache
*.html
  Cache-Control: public, max-age=3600

# API ve dinamik içerik
/api/*
  Cache-Control: no-cache, no-store, must-revalidate

# Service Worker
/sw.js
  Cache-Control: no-cache, no-store, must-revalidate
