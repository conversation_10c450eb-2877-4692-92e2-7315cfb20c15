import { useState, useEffect } from "react";
import { ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";

const ScrollToTopButton = () => {
  const [isVisible, setIsVisible] = useState(false);

  // Sayfa scroll pozisyonunu takip et
  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 200) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  // Sayfanın başına scroll et
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    });
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed bottom-16 right-4 sm:right-6 z-50 animate-fade-in-up">
      <Button
        onClick={scrollToTop}
        variant="thermal"
        size="lg"
        className="rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 w-11 h-11 sm:w-12 sm:h-12 p-0 bg-gradient-to-t from-thermal-bronze to-thermal-bronze-light"
        aria-label="Sayfa başına git"
      >
        <ChevronUp className="w-6 h-6" />
      </Button>
    </div>
  );
};

export default ScrollToTopButton;
